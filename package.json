{"name": "testpad", "version": "0.4.14", "main": "src/index.js", "author": {"email": "<EMAIL>", "name": "Chitkara"}, "license": "MIT", "dependencies": {"axios": "^1.5.1", "better-sqlite3": "^11.10.0", "check-disk-space": "^3.4.0", "dayjs": "^1.11.13", "dotenv": "^16.3.1", "electron-localshortcut": "^3.2.1", "electron-log": "^4.4.8", "electron-store": "^8.1.0", "electron-updater": "^6.1.4", "express": "^5.1.0", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.1", "node-fetch": "^2.7.0", "node-notifier": "^10.0.1", "sequelize": "^6.37.5", "sqlite3": "5.1.6", "umzug": "^3.8.2", "v8-compile-cache": "^2.4.0"}, "devDependencies": {"electron": "^37.4.0"}}