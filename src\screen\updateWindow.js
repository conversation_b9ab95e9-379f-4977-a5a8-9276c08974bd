const { app, BrowserWindow, ipcMain, dialog, globalShortcut } =  require('electron');
const log = require('electron-log');
const { autoUpdater } = require('electron-updater');
const os = require('os');
const path = require('path');
const fs = require('fs');

const config = require('../config');

autoUpdater.logger = log;
autoUpdater.logger.transports.file.level = 'info';

async function updateWindow() {
	try {
		await new Promise((resolve, reject) => {
			try {
				const windowContractorConfig = {
					frame: true,
					alwaysOnTop: false,
					closable: true,
					resizable: true,
					fullscreen: false,
					fullscreenable: true,
					show: true,
					webPreferences: {
						nodeIntegration: true,
						contextIsolation: true,
						devTools: true,
					}
				}
				if (config.env == 'local' || config.env == 'testing') {
					windowContractorConfig.frame = true;
					windowContractorConfig.kiosk = false;
					windowContractorConfig.webPreferences.devTools = true;
				}
				const window = new BrowserWindow(windowContractorConfig);
				
				// Register F11 shortcut for fullscreen toggle
				globalShortcut.register('F11', () => {
					window.setFullScreen(!window.isFullScreen());
				});

				// Clean up shortcut when window is closed
				window.on('closed', () => {
					globalShortcut.unregister('F11');
				});

				window.setContentProtection(false);
				if (config.env == 'local' || config.env === 'testing') {
					window.webContents.openDevTools();
				}
				window.webContents.on('did-finish-load', () => {
					window.maximize();
					window.show();
					window.setVisibleOnAllWorkspaces(true);
				});

				config.programManger.mainWindow = window;
				window.loadFile('public/html/version.html');
				log.info('Update Window open');
				log.info('Platform: ', os.platform());
				
				return resolve();
				
			} catch (error) {
				return reject(error?.message ?? error);
			}
		});
		return;
	} catch (error) {
		console.error(error);
		const response = dialog.showMessageBoxSync(null, {
			title: `Internet connectivity problem.`,
			buttons: ['close', 'retry'],
			message:  error?.message,
			icon: path.join(config.logoPath),
		});
		if (response === 0) {
			return app.exit(0);
		}
		app.relaunch();
		return app.exit(0);
	}
}

module.exports = updateWindow;