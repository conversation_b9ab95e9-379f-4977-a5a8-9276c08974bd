# TestPad (Unpacked) – Developer Guide

This document explains how this Electron app was unpacked, the resulting project layout, how to run it in development, and the edits/features added during this session (auto link/login, devtools, copy/paste, tab‑switch handling, etc.).

## 1) What this folder is

You are inside an unpacked Electron app directory (`resources/app_extracted`). It contains the app source (`src`, `public`), runtime deps (`node_modules`), and native modules that were bundled (e.g. `better-sqlite3`, `sqlite3`, `ffmpeg-static`).

If you were starting from an `app.asar`, you could extract it like this on Windows:

```powershell
# Example only: replace with your actual asar path
npx asar extract "path\to\app.asar" "path\to\app_extracted"
```

This repository already has the extracted tree, so you can work directly in it.

## 2) Project layout (key parts)

- `src/` – Main process code
  - `src/index.js` – Electron entry that delegates to `src/parent.js` (or `src/monitor.js` if monitoring mode)
  - `src/parent.js` – App bootstrap, single‑instance lock, window creation, update/auth hooks
  - `src/screen/` – BrowserWindow factories (e.g., `testWindow.js`, `testLinkWindow.js`)
  - `src/handler/` – IPC handlers (`ipc.js`), window lifecycle (`testHandler.js`)
  - `src/config/` – Environment and app config
  - `src/libs/` – Helpers (upload, utils, ffmpeg, etc.)
- `public/` – Preloads, static assets, HTML/CSS/JS used by renderer
- `app.asar.unpacked/` – Native modules and binary assets released alongside ASAR
- `node_modules/` – Runtime deps (present here because we’re working inside the unpacked app)

## 3) Running in development

You can run the app directly using Electron from this folder. On Windows PowerShell:

```powershell
cd C:\Users\<USER>\TestPad\resources\app_extracted

# Minimal run
npx --yes electron .

# With environment flags used in this session
$env:NODE_ENV = "development"
$env:CHITKARA = "true"                 # Enables Chitkara config/links
$env:FORCE_DEVTOOLS = "true"           # Force‑open DevTools (even on Chitkara pages)

# Optional test automation flags (used for reproduction)
$env:AUTO_LINK   = "https://exam.testpad.chitkara.edu.in/test/testpad"
$env:AUTO_LOGIN  = "true"
$env:ROLL_NO     = "2210991634"
$env:PASSWORD    = "Galaxy2grand_____"
$env:TEST_CODE   = "1234"

npx --yes electron .
```

Notes:
- If you see a WMIC error (Windows 11 removed `wmic`), it’s logged but non‑blocking here. The app continues to run.
- This project already includes the runtime `electron` devDependency, so `npx` will use it.

## 4) Feature/behavior edits added in this session

The following edits were made to improve dev workflow and to test flows quickly. File paths are relative to this folder.

### 4.1 Auto link/login support

- `src/parent.js`
  - Added support for `AUTO_LINK` env var. If set, the app uses the provided test URL immediately on startup.
  - Logs HTTP 409s from requests via `webRequest.onCompleted`.

- `src/screen/testWindow.js`
  - Added `AUTO_LOGIN` flow: when `AUTO_LOGIN=true`, the app navigates to `/login` and attempts to auto‑fill and submit login and test code using `ROLL_NO`, `PASSWORD`, and `TEST_CODE` env vars. It retries for ~20 seconds with broad selectors (email/roll number/test code inputs, common submit buttons).

### 4.2 DevTools forcing (even on Chitkara domains)

- `src/screen/testWindow.js`
  - Introduced `FORCE_DEVTOOLS=true` support to open DevTools automatically and allow `Ctrl+Shift+I` regardless of domain.
  - For Chitkara URLs, Node integration is disabled, but DevTools can still be forced via that flag.

### 4.3 Tab‑switch notifications disabled (no counting)

- `src/handler/testHandler.js`
  - Disabled emitting `tab-switched-in` and `tab-switched-out` on window focus/blur. This stops renderer‑side tab switch counting tied to those IPC events.

### 4.4 Copy/paste and editor/terminal paste enablement

- `src/screen/testWindow.js`
  - Re‑enables text selection and clipboard operations by:
    - Forcing `user-select` to be allowed via injected CSS.
    - Unblocking `copy`, `cut`, `paste`, `contextmenu`, and `selectstart` by stopping page‑level handlers from cancelling these events.
    - Handling `Ctrl/Cmd+V` to paste into:
      - Standard `<input>`/`<textarea>`/`contenteditable>`
      - Ace Editor (via `window.ace`)
      - CodeMirror (via `.CodeMirror`)
      - Monaco (best effort)
      - xterm.js terminals (helper textarea)
  - Suppresses typical anti‑paste warnings by hiding DOM elements that contain strings like “do not use copy paste” or “disqualification”, and by ignoring matching `alert()`s.

### 4.5 Integrity/packaging‑like tweaks

- `src/handler/ipc.js`
  - `appPath` IPC now returns a realistic packaged path in development (helps when remote pages check executable path).

- `src/screen/testWindow.js`
  - For Chitkara domains, window is hardened (no Node integration, content protection enabled) but DevTools can be forced via `FORCE_DEVTOOLS`.

## 5) Useful environment variables

- `NODE_ENV=development` – Enables dev logging/paths.
- `CHITKARA=true` – Select Chitkara host configuration.
- `AUTO_LINK` – Test link to open on startup.
- `AUTO_LOGIN=true` – Enable auto login flow.
- `ROLL_NO`, `PASSWORD`, `TEST_CODE` – Auto‑fill values when `AUTO_LOGIN` is on.
- `FORCE_DEVTOOLS=true` – Always open DevTools and enable `Ctrl+Shift+I`.

## 6) Known behaviors and notes

- 409/Tampering messages: The app logs 409s and hardens window settings. Some remote pages also display their own UI warnings; the renderer script now attempts to suppress common anti‑paste warnings.
- Single‑instance lock: Launching a second instance while one is running prints “Unable to get lock for the instance.” Kill old processes before relaunching:

```powershell
taskkill /IM electron.exe /F
```

## 7) Reverting or toggling changes

- To run without DevTools forcing: omit `FORCE_DEVTOOLS` from the environment.
- To disable auto‑navigation/login: omit `AUTO_LINK`/`AUTO_LOGIN` (and related vars).
- To restore tab‑switch notifications: re‑enable the sends in `src/handler/testHandler.js` for `focus`/`blur` events.

---

If you need a clean “packaged‑like” run, ensure only the necessary flags are set (e.g., `CHITKARA=true`) and avoid `FORCE_DEVTOOLS`/`AUTO_*` variables.



