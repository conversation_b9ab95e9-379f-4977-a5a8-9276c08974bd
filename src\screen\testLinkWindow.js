const { BrowserWindow, dialog, webFrame, systemPreferences, globalShortcut } =  require('electron');
const log = require('electron-log');
const path = require('path');
const os = require('os');

console = log;

const config = require('../config');
const libs = require('../libs');
const handler = require('../handler');

function testWindow() {
    /** @type {import('electron').BrowserWindowConstructorOptions} */
    const windowContractorConfig = {
        frame: true,
        alwaysOnTop: false,
        closable: true,
        resizable: true,
        fullscreen: false,
        fullscreenable: true,
        show: true,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: true,
            devTools: true,
            preload: path.join( __dirname,"../../public/preload/testLinkWindow.js"),
            // Enable screen sharing
            enableBlinkFeatures: 'ScreenCapture,GetDisplayMedia',
            // Allow screen capture
            enableScreenCapture: true,
            // Allow desktop capture
            enableDesktopCapture: true,
        },
    }
    if (config.env == 'local' || config.env == 'testing') {
        windowContractorConfig.frame = true;
        windowContractorConfig.kiosk = false;
        windowContractorConfig.webPreferences.devTools = true;
    }
    const window = new BrowserWindow(windowContractorConfig);
    
    // Register F11 shortcut for fullscreen toggle
    globalShortcut.register('F11', () => {
        window.setFullScreen(!window.isFullScreen());
    });

    // Clean up shortcut when window is closed
    window.on('closed', () => {
        globalShortcut.unregister('F11');
    });

    window.setContentProtection(false);
    if (config.env == 'local' || config.env === 'testing') {
        window.webContents.openDevTools();
    }
    window.webContents.on('did-finish-load', () => {
        window.maximize();
        window.show();
        window.setVisibleOnAllWorkspaces(true);
    });
    window.loadFile('public/html/index.html');
    return window;
}

module.exports = testWindow;