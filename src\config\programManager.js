const { Notification, app } = require('electron');
const path = require('path');
const fs = require('fs');
const { utils } = require('../libs');

module.exports = class ProgramManager {
    /** @type { Electron.BrowserWindow | null } */
    #mainWindow

    /** @type {string | null} */
    #quizLink

    /** @type {string | null} */
    #notificationIconPath
    
    /** @type {string | null} */
    #quizId

    /** @type {string | null} */
    #urlWhereLoadFailed

    constructor(iconPath) {
        this.#mainWindow = null;
        this.#notificationIconPath = iconPath;
        this.#quizId = null;
        this.#urlWhereLoadFailed = null;
    }

    /** @param { import('electron').NotificationConstructorOptions } options */
    notify(options) {
        if (!options.icon) options.icon = this.#notificationIconPath;
        new Notification(options);
    }

    /** @param {boolean | number} value */
    zoomChange(value) {
        if (!this.#mainWindow) {
            console.info(new Error('Window is empty not able to change zoom'));
            return;
        }
        const previousValue = this.#mainWindow.webContents.getZoomFactor();
        let valueToSet = previousValue;

        if (typeof value === 'boolean') {
            valueToSet = previousValue + (value ? 0.1 : -0.1);
        }
        if (typeof value === 'number') {
            valueToSet = value;
        }

        this.#mainWindow.webContents.setZoomFactor(valueToSet);
        console.info(`Zoom set to:`, valueToSet);
    }

    closeActiveWindow() {
        if (this.#mainWindow) {
            this.#mainWindow.close();
            this.#mainWindow = null;
        }
    }

    get mainWindow() {
        return this.#mainWindow;
    }

    set mainWindow(window) {
        if (this.#mainWindow) {
            this.#mainWindow.close();
        }
        this.#mainWindow = window;
    }

    get quizLink() {
        return this.#quizLink;
    }

    set quizLink(link) {
        if (this.#mainWindow) {
            this.#mainWindow.setAlwaysOnTop(false);
        }
        this.#quizLink = link;
    }

    setQuizId(value) {
        this.#quizId = value;
    }

    // ✅ Always allow monitoring/disk/VM checks
    async checkForMonitor() { return true; }
    async startMonitoringApp() { return true; }
    async continuouslyEmitMonitoringNotWorking() { return; }
    async checkForFullDiskAccess() { return true; }
    async checkIfVM() { return false; }
    async checkForDiskSpaceForRecording() { return true; }

    getJitsiConfig() {
        try {
            const pathToJitsiConfig = path.join(app.getPath('userData'), 'jitsi');
            const data = fs.readFileSync(pathToJitsiConfig);
            if (data) {
                return JSON.parse(data.toString());
            }
        } catch (error) {
            console.log(error);
        }
        return {};
    }

    setJitsiConfig(dataToSave) {
        try {
            const pathToJitsiConfig = path.join(app.getPath('userData'), 'jitsi');
            fs.writeFileSync(pathToJitsiConfig, JSON.stringify(dataToSave));
            return true;
        } catch (err) {
            console.log(err);
            return false;
        }
    }

    getPlatformName() {
        return 'MyApp'; // ✅ replace with your app name
    }

    get urlWhereLoadFailed() {
        return this.#urlWhereLoadFailed;
    }

    set urlWhereLoadFailed(url) {
        this.#urlWhereLoadFailed = url;
    }

    retryPageLoad() {
        if (this.mainWindow && this.#urlWhereLoadFailed) {
            this.mainWindow.loadURL(this.#urlWhereLoadFailed);
        }
    }
};
