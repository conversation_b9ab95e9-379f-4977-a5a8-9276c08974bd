const { BrowserWindow, dialog, webFrame, systemPreferences, globalShortcut } =  require('electron');
const log = require('electron-log');
const path = require('path');
const os = require('os');

console = log;

const config = require('../config');
const libs = require('../libs');
const handler = require('../handler');

function testWindow(link) {
	const isChitDomain = typeof link === 'string' && (link.includes('testpad.chitkara.edu.in') || link.includes('testpad.chitkarauniversity.edu.in'));
	/** @type {import('electron').BrowserWindowConstructorOptions} */
	const windowContractorConfig = {
		frame: true,
		alwaysOnTop: false,
		closable: true,
		resizable: true,
		fullscreen: false,
		fullscreenable: true,
		show: false,
		webPreferences: {
			nodeIntegration: isChitDomain ? false : true,
			contextIsolation: true,
			devTools: (process.env.FORCE_DEVTOOLS === 'true') ? true : (isChitDomain ? false : true),
			preload: path.join( __dirname,"../../public/preload/testWindow.js"),
			// Enable screen sharing
			enableBlinkFeatures: 'ScreenCapture,GetDisplayMedia',
			// Allow screen capture
			enableScreenCapture: true,
			// Allow desktop capture
			enableDesktopCapture: true,
			// Enable clipboard access
			enableClipboard: true,
			// Allow paste operations
			allowRunningInsecureContent: isChitDomain ? false : true,
			// Enable web security but allow clipboard
			webSecurity: true,
			// Enable clipboard access
			clipboard: true,
			// Enable text selection
			enableTextSelection: true
		},
	}
	if (config.env == 'local' || config.env == 'testing') {
		windowContractorConfig.frame = true;
		windowContractorConfig.kiosk = false;
		windowContractorConfig.webPreferences.devTools = true;
	}
	const window = new BrowserWindow(windowContractorConfig);

	// Standardize user agent to a typical Chrome on Windows to avoid detection
	try {
		const standardUA = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
		window.webContents.setUserAgent(standardUA);
	} catch (err) {}
	
	// Function to handle fullscreen toggle
	const toggleFullscreen = () => {
		const isFullScreen = window.isFullScreen();
		window.setFullScreen(!isFullScreen);
		window.setMenuBarVisibility(isFullScreen);
	};

	// Register F11 shortcut for fullscreen toggle
	const registerF11Shortcut = () => {
		// Unregister any existing F11 shortcut first
		globalShortcut.unregister('F11');
		// Register the new F11 shortcut
		globalShortcut.register('F11', toggleFullscreen);
	};

	// Initial registration of F11 shortcut
	registerF11Shortcut();

	// Clean up shortcut when window is closed
	window.on('closed', () => {
		globalShortcut.unregister('F11');
	});

	window.setContentProtection(isChitDomain ? true : false);
	if ((config.env == 'local' || config.env === 'testing') || process.env.FORCE_DEVTOOLS === 'true') {
		try { window.webContents.openDevTools({ mode: 'detach' }); } catch (e) {}
	}

	// Handle all navigation events
	const handleNavigation = () => {
		window.maximize();
		window.show();
		window.setVisibleOnAllWorkspaces(true);
		window.setMenuBarVisibility(false);
		// Re-register F11 shortcut after navigation
		registerF11Shortcut();
	};

	// Handle page load and navigation
	window.webContents.on('did-finish-load', handleNavigation);
	window.webContents.on('did-navigate', handleNavigation);
	window.webContents.on('did-navigate-in-page', handleNavigation);

	// Optional: Auto-login flow for testing (driven by env vars)
	try {
		const shouldAutoLogin = String(process.env.AUTO_LOGIN || '').toLowerCase() === 'true';
		if (shouldAutoLogin) {
			const maybeGoToLogin = () => {
				try {
					const currentUrl = window.webContents.getURL();
					if (currentUrl && currentUrl.includes('/test/') && !currentUrl.includes('/login')) {
						const loginUrl = currentUrl.endsWith('/') ? (currentUrl + 'login') : (currentUrl + '/login');
						window.webContents.loadURL(loginUrl).catch(() => {});
					}
				} catch (err) {}
			};
			const attemptAutoFill = () => {
				try {
					const rollNo = process.env.ROLL_NO || '';
					const password = process.env.PASSWORD || '';
					const testCode = process.env.TEST_CODE || '';
					const script = `
						(function(){
							if (window.__AUTO_LOGIN_DONE__) return 'skipped';
							try {
								const sel = (s) => document.querySelector(s);
								const byText = (tag, txt) => {
									const nodes = Array.from(document.querySelectorAll(tag));
									return nodes.find(n => (n.innerText||'').toLowerCase().includes(txt));
								};
								const candidates = {
									roll: [
										'input[name=\\"roll\\"]','input[id*=\\"roll\\"]','input[name*=\\"roll\\"]','input[placeholder*=\\"roll\\" i]',
										'input[name*=\\"email\\"]','input[id*=\\"email\\"]','input[placeholder*=\\"email\\" i]',
										'input[name*=\\"user\\"]','input[id*=\\"user\\"]','input[name*=\\"enroll\\"]','input[id*=\\"enroll\\"]',
										'input[name*=\\"rollnumber\\"]','input[id*=\\"rollnumber\\"]','input[name*=\\"roll_number\\"]'
									],
									pass: [
										'input[type=\\"password\\"]','input[name*=\\"pass\\"]','input[id*=\\"pass\\"]','input[placeholder*=\\"password\\" i]'
									],
									code: [
										'input[name=\\"code\\"]','input[id*=\\"code\\"]','input[placeholder*=\\"code\\" i]','input[name*=\\"test\\"]','input[id*=\\"test\\"]',
										'input[name*=\\"quiz\\"]','input[id*=\\"quiz\\"]','input[placeholder*=\\"test code\\" i]'
									]
								};
								function pick(list){ for(const s of list){ const el = sel(s); if(el && el.offsetParent !== null) return el; } return null; }
								const r = pick(candidates.roll);
								const p = pick(candidates.pass);
								const c = pick(candidates.code);
								const setVal = (el, val) => {
									try {
										const proto = Object.getPrototypeOf(el);
										const desc = Object.getOwnPropertyDescriptor(proto, 'value');
										if (desc && desc.set) { desc.set.call(el, val); }
										else { el.value = val; }
										el.dispatchEvent(new Event('input', { bubbles: true }));
										el.dispatchEvent(new Event('change', { bubbles: true }));
										el.blur();
									} catch(_) { el.value = val; }
								};
								let filled = false;
								if ('${rollNo}'.length && r) { r.focus(); setVal(r, '${rollNo}'); filled = true; }
								if ('${password}'.length && p) { p.focus(); setVal(p, '${password}'); filled = true; }
								if ('${testCode}'.length && c) { c.focus(); setVal(c, '${testCode}'); filled = true; }
								if (filled) {
									let submit = sel('button[type=\\"submit\\"], input[type=\\"submit\\"], button#goToBtn, button[name*=\\"login\\" i], button[id*=\\"login\\" i]');
									if (!submit) submit = Array.from(document.querySelectorAll('button')).find(b => (b.innerText||'').toLowerCase().includes('sign in') || (b.innerText||'').toLowerCase().includes('login'));
									if (submit) submit.click();
									window.__AUTO_LOGIN_DONE__ = true;
									return 'done';
								}
								return 'retry';
							} catch(e) { return 'error'; }
						})();
					`;
					return window.webContents.executeJavaScript(script).catch(() => 'error');
				} catch (err) { return Promise.resolve('error'); }
			};
			window.webContents.on('did-finish-load', async () => {
				maybeGoToLogin();
				const start = Date.now();
				let result = 'retry';
				while(Date.now() - start < 20000 && result === 'retry') {
					result = await attemptAutoFill();
					if (result === 'done') break;
					await new Promise(r => setTimeout(r, 800));
				}
			});
		}
	} catch (err) {}

	// Handle fullscreen changes
	window.on('enter-full-screen', () => {
		window.setMenuBarVisibility(false);
	});

	window.on('leave-full-screen', () => {
		window.setMenuBarVisibility(true);
	});

	// Enable clipboard access and prevent attention message (dev-only tweaks)
	if (!isChitDomain) window.webContents.on('dom-ready', () => {
		window.webContents.executeJavaScript(`
			// Try to find common code editor containers
			const editorSelectors = [
				'.monaco-editor', '.ace_editor', '.CodeMirror', 'textarea', 'pre[contenteditable="true"]'
			];
			function addSafeListenersToEditor() {
				let editor = null;
				for (const sel of editorSelectors) {
					editor = document.querySelector(sel);
					if (editor) break;
				}
				if (editor) {
					// Enable text selection and editing
					editor.style.userSelect = 'text';
					editor.contentEditable = true;
					// Add safe paste/copy/cut listeners
					['paste', 'copy', 'cut'].forEach(eventType => {
						editor.addEventListener(eventType, e => {
							e.stopPropagation();
							// Allow default behavior
						}, true);
					});
				}
			}
			// Add listeners on load
			addSafeListenersToEditor();
			// Also observe for dynamic editor changes (e.g., navigation)
			const observer = new MutationObserver(() => {
				addSafeListenersToEditor();
			});
			observer.observe(document.body, { childList: true, subtree: true });
		`);
	});

	// Neutralize common restriction hooks on exam pages (visibility/blur/fullscreen/tab-switch)
	window.webContents.on('dom-ready', () => {
		const script = `(() => {
			try {
				// 1) Fake visibility: always visible
				const alwaysFalse = () => false;
				['hidden','webkitHidden','mozHidden','msHidden'].forEach((prop) => {
					try {
						Object.defineProperty(document, prop, { configurable: true, get: alwaysFalse });
					} catch(_) {}
				});
				// 2) Ignore visibility/blur/focus/fullscreen change listeners
				const blockEvents = new Set(['visibilitychange','webkitvisibilitychange','mozvisibilitychange','msvisibilitychange','blur','focus','fullscreenchange','webkitfullscreenchange','mozfullscreenchange','MSFullscreenChange']);
				const origAdd = Document.prototype.addEventListener;
				Document.prototype.addEventListener = function(type, listener, options) {
					if (blockEvents.has(String(type).toLowerCase())) return; 
					return origAdd.call(this, type, listener, options);
				};
				const winOrigAdd = Window.prototype.addEventListener;
				Window.prototype.addEventListener = function(type, listener, options) {
					if (blockEvents.has(String(type).toLowerCase())) return; 
					return winOrigAdd.call(this, type, listener, options);
				};
				// 3) Stub fullscreen detection as if already fullscreen
				['fullscreenElement','webkitFullscreenElement','mozFullScreenElement','msFullscreenElement'].forEach((prop) => {
					try { Object.defineProperty(document, prop, { configurable: true, get: () => document.body }); } catch(_) {}
				});
				// 4) Soften window.open devtools checks (optional)
				try { Object.defineProperty(navigator, 'webdriver', { configurable: true, get: () => undefined }); } catch(_) {}
				// 5) Re-enable selection and clipboard operations
				try {
					const style = document.createElement('style');
					style.textContent = '* { user-select: text !important; -webkit-user-select: text !important; -moz-user-select: text !important; -ms-user-select: text !important; }';
					document.head.appendChild(style);
				} catch(_) {}
				['copy','cut','paste','contextmenu','selectstart','keydown'].forEach((type) => {
					document.addEventListener(type, function(e){
						if (type === 'keydown') {
							const k = (e.key||'').toLowerCase();
							if ((e.ctrlKey||e.metaKey) && (k==='c' || k==='x' || k==='v' || k==='a')) {
								// Allow system shortcuts and prevent page handlers from blocking
								e.stopImmediatePropagation();
								return;
							}
						} else {
							// Prevent site-level handlers from cancelling clipboard/context menu
							e.stopImmediatePropagation();
						}
					}, true);
				});
				try { document.oncopy = null; document.oncut = null; document.onpaste = null; document.oncontextmenu = null; } catch(_) {}

				// 6) Suppress app IPC signals for closeability/tab switching if exposed
				if (window.api && typeof window.api.sendToMain === 'function') {
					const origSend = window.api.sendToMain;
					window.api.sendToMain = (channel, data) => {
						const deny = ['change-closeable-state','tab-switched-in','tab-switched-out','pre-login-test'];
						if (deny.includes(String(channel))) return true;
						return origSend(channel, data);
					};
				}
			} catch (e) { /* ignore */ }
		})();`;
		try { window.webContents.executeJavaScript(script); } catch (e) {}
		// Add robust paste handler for editors and terminals
		const pasteScript = `(() => {
			function insertIntoActive(text) {
				let handled = false;
				const active = document.activeElement;
				const isTextInput = (el) => el && el.tagName === 'TEXTAREA' || (el && el.tagName === 'INPUT' && (!el.type || el.type === 'text' || el.type === 'search' || el.type === 'password' || el.type === 'email' || el.type === 'number'));
				try {
					if (isTextInput(active)) {
						const start = active.selectionStart ?? active.value.length;
						const end = active.selectionEnd ?? active.value.length;
						if (typeof active.setRangeText === 'function') {
							active.setRangeText(text, start, end, 'end');
						} else {
							active.value = active.value.slice(0, start) + text + active.value.slice(end);
						}
						handled = true;
					} else if (active && (active.isContentEditable || active.getAttribute('contenteditable') === 'true')) {
						document.execCommand('insertText', false, text);
						handled = true;
					} else {
						// Ace Editor
						const aceRoot = (active && active.closest && active.closest('.ace_editor')) || document.querySelector('.ace_editor');
						if (aceRoot && window.ace && typeof window.ace.edit === 'function') {
							try { const ed = window.ace.edit(aceRoot); ed.focus(); ed.insert(text); handled = true; } catch(_) {}
						}
						// CodeMirror
						const cmRoot = (active && active.closest && active.closest('.CodeMirror')) || document.querySelector('.CodeMirror');
						if (!handled && cmRoot && cmRoot.CodeMirror) {
							try { cmRoot.CodeMirror.focus(); cmRoot.CodeMirror.replaceSelection(text, 'end'); handled = true; } catch(_) {}
						}
						// Monaco (best effort)
						const monacoRoot = (active && active.closest && active.closest('.monaco-editor')) || document.querySelector('.monaco-editor');
						if (!handled && monacoRoot) {
							try { monacoRoot.focus(); document.execCommand('insertText', false, text); handled = true; } catch(_) {}
						}
						// xterm.js terminal
						const termRoot = (active && active.closest && active.closest('.xterm')) || document.querySelector('.xterm');
						if (!handled && termRoot) {
							// xterm listens to paste on the textarea inside .xterm-helper-t...
							try {
								const helper = termRoot.querySelector('textarea, .xterm-helper-textarea');
								if (helper) {
									helper.focus();
									const s = helper.selectionStart ?? helper.value.length;
									helper.value = (helper.value || '');
									helper.setRangeText(text, s, s, 'end');
									helper.dispatchEvent(new Event('input', { bubbles: true }));
									handled = true;
								}
							} catch(_) {}
						}
					}
				} catch(_) {}
				if (!handled) {
					try { document.execCommand('insertText', false, text); } catch(_) {}
				}
				return handled;
			}
			document.addEventListener('keydown', async function(e){
				try {
					const isPaste = (e.ctrlKey||e.metaKey) && String(e.key||'').toLowerCase() === 'v';
					if (!isPaste) return;
					e.stopImmediatePropagation();
					const text = await (navigator.clipboard && navigator.clipboard.readText ? navigator.clipboard.readText() : Promise.resolve(''));
					if (text) insertIntoActive(text);
				} catch(_) {}
			}, true);
		})();`;
		try { window.webContents.executeJavaScript(pasteScript); } catch (e) {}

		// Suppress anti-paste warning banners/modals
		const suppressScript = `(() => {
			try {
				// 1) Mute specific alert text
				const origAlert = window.alert;
				window.alert = function(msg){
					try {
						const s = String(msg||'').toLowerCase();
						if (s.includes('do not use copy paste') || s.includes('disqualification')) return;
					} catch(_) {}
					return origAlert.apply(this, arguments);
				};
				// 2) Remove DOM warnings containing the text
				function hideWarnings(root){
					try {
						const nodes = root ? [root] : Array.from(document.querySelectorAll('body *'));
						nodes.forEach(n => {
							try {
								const txt = (n.innerText||n.textContent||'').toLowerCase();
								if (txt.includes('do not use copy paste') || txt.includes('disqualification')) {
									n.style.setProperty('display','none','important');
									n.style.setProperty('visibility','hidden','important');
									n.setAttribute('data-suppressed','copy-paste-warning');
								}
							} catch(_) {}
						});
					} catch(_) {}
				}
				hideWarnings();
				const mo = new MutationObserver(muts => {
					for (const m of muts) {
						if (m.addedNodes) {
							m.addedNodes.forEach(n => { if (n && n.nodeType === 1) hideWarnings(n); });
						}
					}
				});
				mo.observe(document.documentElement, { childList: true, subtree: true });
			} catch(_) {}
		})();`;
		try { window.webContents.executeJavaScript(suppressScript); } catch (e) {}
	});

	// Enable right-click context menu (dev only)
	if (!isChitDomain) window.webContents.on('context-menu', (e, params) => {
		window.webContents.executeJavaScript(`document.dispatchEvent(new MouseEvent('contextmenu', {bubbles: true, cancelable: true, view: window}));`);
	});

	// Register Ctrl+Shift+I to open DevTools (dev only or forced)
	if (!isChitDomain || process.env.FORCE_DEVTOOLS === 'true') globalShortcut.register('CommandOrControl+Shift+I', () => {
		try { window.webContents.openDevTools({ mode: 'detach' }); } catch (e) {}
	});

	// Ace Editor simulate typing patch (dev only)
	if (!isChitDomain) window.webContents.on('dom-ready', () => {
		window.webContents.executeJavaScript(`
			// Register Ctrl+Alt+V to simulate typing clipboard into the first editable Ace Editor
			document.addEventListener('keydown', async function(e) {
				if (e.ctrlKey && e.altKey && e.key.toLowerCase() === 'v') {
					try {
						const clipboardText = await navigator.clipboard.readText();
						if (window.ace && typeof window.ace.edit === 'function') {
							// Find all Ace Editor containers
							const aceEditors = Array.from(document.querySelectorAll('.ace_editor, [class*=ace_editor]'));
							let editableEditor = null;
							for (const el of aceEditors) {
								try {
									const editorInstance = window.ace.edit(el);
									// Check if editor is not read-only
									if (!editorInstance.getReadOnly && !editorInstance.getOption) continue;
									const isReadOnly = editorInstance.getReadOnly ? editorInstance.getReadOnly() : editorInstance.getOption('readOnly');
									if (!isReadOnly) {
										editableEditor = editorInstance;
										break;
									}
								} catch (err) { /* skip if error */ }
							}
							if (editableEditor) {
								editableEditor.focus();
								// Simulate typing each character
								let i = 0;
								function typeChar() {
									if (i < clipboardText.length) {
										editableEditor.insert(clipboardText[i]);
										i++;
										setTimeout(typeChar, 20); // 20ms per character
									}
								}
								typeChar();
							} else {
								alert('Editable Ace Editor not found on this page.');
							}
						}
					} catch (err) {
						alert('Clipboard read failed: ' + err);
					}
				}
			});
		`);
	});

	// Re-enable text selection patch (dev only)
	if (!isChitDomain) window.webContents.on('dom-ready', () => {
		window.webContents.executeJavaScript(`
			// Re-enable text selection
			const style = document.createElement('style');
			style.textContent = '* { user-select: auto !important; -webkit-user-select: auto !important; -moz-user-select: auto !important; -ms-user-select: auto !important; }';
			document.head.appendChild(style);
			// Allow copy event
			document.addEventListener('copy', function(e) { e.stopPropagation(); }, true);
		`);
	});

	// Add error handling for failed loads
	window.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
		log.error('Failed to load:', errorCode, errorDescription);
		dialog.showErrorBox('Loading Error', 'Failed to load the page. Please check your internet connection and try again.');
	});

	// Show window only when ready
	window.on('ready-to-show', () => {
		window.show();
	});

	// Load the URL
	if (link) {
		window.loadURL(link);
	} else {
		window.loadFile('public/html/index.html');
	}

	// Ensure fullscreen after a short delay to handle any initial loading
	setTimeout(() => {
		if (!window.isFullScreen()) {
			window.setFullScreen(true);
		}
		// Re-register F11 shortcut after initial load
		registerF11Shortcut();
	}, 1000);

	return window;
}

module.exports = testWindow;