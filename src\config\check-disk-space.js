const os = require('os');
const { exec } = require('child_process');
const fs = require('fs');

function checkDiskSpace(location) {
    return new Promise((resolve, reject) => {
        if (os.platform() === 'win32') {
            exec(`wmic logicaldisk where "DeviceID='${location.charAt(0)}:'" get FreeSpace,Size /value`, (error, stdout) => {
                if (error) {
                    reject(error);
                    return;
                }
                const freeSpace = parseInt(stdout.match(/FreeSpace=(\d+)/)?.[1] || '0');
                const totalSize = parseInt(stdout.match(/Size=(\d+)/)?.[1] || '0');
                resolve({
                    free: freeSpace,
                    size: totalSize
                });
            });
        } else {
            fs.statfs(location, (err, stats) => {
                if (err) {
                    reject(err);
                    return;
                }
                resolve({
                    free: stats.bfree * stats.bsize,
                    size: stats.blocks * stats.bsize
                });
            });
        }
    });
}

module.exports = checkDiskSpace;
module.exports.default = checkDiskSpace; 